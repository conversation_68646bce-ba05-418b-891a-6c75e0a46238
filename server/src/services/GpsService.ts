import { PrismaClient } from '@prisma/client';

// GPS Device Data from smartelock.net:8088/device/list_all
export interface GpsDeviceData {
  vehicleId: number;
  vehicleName: string;
  deviceId: string;
  groupName: string;
  acc: number;
  devType: number;
  devTypeName: string | null;
  lng: number;
  lat: number;
  locate: number;
  gsmMode: string | null;
  updateTime: string;
  gpsTime: string;
  exBytes: string;
  speed: number;
  event: string;
  mileage: number;
  status: string;
  direction: string;
  online: boolean;
  devTemp: number;
  lockStatus: number;
  backCapStatus: number;
  steelStringStatus: number;
  todayMaxSpeed: number;
  devBatteryPCT: number;
  todayIdlingSeconds: number;
  motorLockStatus: number;
}

export interface GpsApiResponse {
  code: number;
  data: GpsDeviceData[];
  message: string;
}

export interface GpsLoginResponse {
  code: number;
  data: {
    token?: string;
    tokenHead?: string;
    [key: string]: any;
  };
  message: string;
}

export class GpsService {
  private prisma: PrismaClient;
  private baseUrl: string;
  private credentials: {
    username: string;
    password: string;
  };
  private authToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.baseUrl = process.env.GPS_API_BASE_URL || 'http://smartelock.net:8088';
    this.credentials = {
      username: process.env.GPS_API_USERNAME || '',
      password: process.env.GPS_API_PASSWORD || ''
    };

    if (!this.credentials.username || !this.credentials.password) {
      console.warn('⚠️ GPS API credentials not found in environment variables');
      console.warn('💡 Please set GPS_API_USERNAME and GPS_API_PASSWORD in .env file');
    }
  }

  /**
   * Login to GPS API and get authentication token with retry mechanism
   */
  private async login(retryCount = 0): Promise<string> {
    const maxRetries = 3;
    console.log(`🔐 Logging in to GPS API... (attempt ${retryCount + 1}/${maxRetries + 1})`);

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`${this.baseUrl}/user/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          username: this.credentials.username,
          password: this.credentials.password
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`GPS API login failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as GpsLoginResponse;

      // GPS API returns code 200 for success, not 0
      if (result.code !== 200 || !result.data.token) {
        throw new Error(`GPS API login failed: ${result.message}`);
      }

      this.authToken = result.data.token;
      // Set token expiry to 1 hour from now (adjust based on actual API behavior)
      this.tokenExpiry = new Date(Date.now() + 60 * 60 * 1000);

      console.log('✅ GPS API login successful');
      return this.authToken;
    } catch (error) {
      console.error(`❌ GPS API login error (attempt ${retryCount + 1}):`, error);

      // Retry logic
      if (retryCount < maxRetries && error instanceof Error) {
        const isRetryableError =
          error.name === 'AbortError' ||
          error.message.includes('ECONNRESET') ||
          error.message.includes('ETIMEDOUT') ||
          error.message.includes('500') ||
          error.message.includes('502') ||
          error.message.includes('503');

        if (isRetryableError) {
          const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
          console.log(`⏳ Retrying GPS login in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.login(retryCount + 1);
        }
      }

      throw error;
    }
  }

  /**
   * Get valid authentication token (login if needed)
   */
  private async getValidToken(): Promise<string> {
    // Check if we have a valid token
    if (this.authToken && this.tokenExpiry && this.tokenExpiry > new Date()) {
      return this.authToken;
    }

    // Login to get new token
    return await this.login();
  }

  /**
   * Get all devices from GPS API
   */
  async getAllDevices(): Promise<GpsDeviceData[]> {
    console.log('📍 Fetching all devices from GPS API...');

    try {
      const token = await this.getValidToken();

      const response = await fetch(`${this.baseUrl}/device/list_all`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          timeOffset: 0 // As per lock.md example
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`GPS API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json() as GpsApiResponse;

      // According to lock.md, success code is likely 0 or 200. Assuming 200 from previous code.
      if (result.code !== 200) {
        // If login works but list_all fails, it might be a token issue.
        if (result.code === 401) { // Unauthorized
            console.warn('⚠️ GPS API returned 401. Forcing re-login.');
            this.authToken = null; // Invalidate token
            // Consider a single retry here if needed, or let the next cron run handle it.
        }
        throw new Error(`GPS API error: [Code ${result.code}] ${result.message}`);
      }

      console.log(`✅ Retrieved ${result.data.length} devices from GPS API`);
      return result.data || []; // Ensure it returns an array even if data is null
    } catch (error) {
      console.error('❌ Error fetching devices from GPS API:', error);
      throw error;
    }
  }

  /**
   * Get GPS position data for cronjob - simplified version
   */
  async getGpsPositionData(): Promise<GpsDeviceData[]> {
    console.log('📍 Fetching GPS position data for cronjob...');

    try {
      const devices = await this.getAllDevices();
      console.log(`✅ Retrieved ${devices.length} GPS devices for position update`);
      return devices;
    } catch (error) {
      console.error('❌ Error fetching GPS position data:', error);
      throw error;
    }
  }

  /**
   * Find GPS device for E-Seal using multiple mapping strategies
   * Priority: gpsDeviceId > noImei > noEseal > partial matches
   */
  private findGpsDeviceForESeal(eseal: any, devices: GpsDeviceData[]): GpsDeviceData | null {
    console.log(`📍 Searching GPS device for E-Seal: ${eseal.noEseal} (IMEI: ${eseal.noImei}, GPS Device ID: ${eseal.gpsDeviceId})`);

    // Strategy 1: Match by deviceId = gpsDeviceId (NEW - highest priority)
    if (eseal.gpsDeviceId) {
      let device = devices.find(d => d.deviceId === eseal.gpsDeviceId);
      if (device) {
        console.log(`✅ Found GPS device by GPS Device ID match: ${device.deviceId} = ${eseal.gpsDeviceId}`);
        return device;
      }
    }

    // Strategy 2: Match by deviceId = noImei (fallback method)
    let device = devices.find(d => d.deviceId === eseal.noImei);
    if (device) {
      console.log(`✅ Found GPS device by IMEI match: ${device.deviceId} = ${eseal.noImei}`);
      return device;
    }

    // Strategy 3: Match by vehicleName = noEseal
    device = devices.find(d => d.vehicleName === eseal.noEseal);
    if (device) {
      console.log(`✅ Found GPS device by E-Seal name match: ${device.vehicleName} = ${eseal.noEseal}`);
      return device;
    }

    // Strategy 4: Match by deviceId = noEseal (alternative)
    device = devices.find(d => d.deviceId === eseal.noEseal);
    if (device) {
      console.log(`✅ Found GPS device by E-Seal as deviceId: ${device.deviceId} = ${eseal.noEseal}`);
      return device;
    }

    // Strategy 5: Partial match on vehicleName containing noEseal
    device = devices.find(d => d.vehicleName && d.vehicleName.includes(eseal.noEseal));
    if (device) {
      console.log(`✅ Found GPS device by partial name match: ${device.vehicleName} contains ${eseal.noEseal}`);
      return device;
    }

    // Strategy 5: Partial match on deviceId containing noEseal
    device = devices.find(d => d.deviceId && d.deviceId.includes(eseal.noEseal));
    if (device) {
      console.log(`✅ Found GPS device by partial deviceId match: ${device.deviceId} contains ${eseal.noEseal}`);
      return device;
    }

    console.warn(`⚠️ No GPS device found for E-Seal ${eseal.noEseal} using any mapping strategy`);
    console.log(`Available GPS devices:`, devices.map(d => ({ deviceId: d.deviceId, vehicleName: d.vehicleName })));

    return null;
  }

  /**
   * Get GPS position data for E-Seal devices and format for Beacukai API
   */
  async getESealPositionData(esealId: string): Promise<{
    latitude: string;
    longitude: string;
    address: string;
    altitude: string;
    speed: string;
    battery: string;
    event: string;
    lockStatus: string;
    signalStrength: string;
    temperature: string;
    mileage: string;
    direction: string;
    updateTime: string;
  } | null> {
    try {
      // Get E-Seal from database to find device mapping
      const eseal = await this.prisma.eSeal.findUnique({
        where: { id: esealId }
      });

      if (!eseal) {
        console.error(`❌ E-Seal not found: ${esealId}`);
        return null;
      }

      // Get GPS data for all devices
      const devices = await this.getAllDevices();

      if (devices.length === 0) {
        console.warn(`⚠️ No GPS devices found from API`);
        return null;
      }

      // Find matching GPS device using multiple strategies
      const device = this.findGpsDeviceForESeal(eseal, devices);

      if (!device) {
        console.warn(`⚠️ No matching GPS device found for E-Seal: ${eseal.noEseal}`);
        return null;
      }

      // Map lock status to event codes (based on seal.md LAMPIRAN 2)
      let eventCode = '0'; // Default: Normal
      if (!device.online) {
        eventCode = '1'; // Offline/Locked
      } else if (device.lockStatus === 0) {
        eventCode = '2'; // Unlocked
      } else if (device.steelStringStatus === 0) {
        eventCode = '3'; // Cut the rope
      }

      // Format data with comprehensive GPS information
      return {
        latitude: device.lat.toString(),
        longitude: device.lng.toString(),
        address: `GPS Location: ${device.lat}, ${device.lng}`, // Will be converted by geocoding service
        altitude: device.locate?.toString() || '0', // Use locate as altitude approximation
        speed: device.speed.toString(),
        battery: device.devBatteryPCT.toString(),
        event: eventCode,
        lockStatus: device.lockStatus.toString(),
        signalStrength: device.gsmMode || '0',
        temperature: device.devTemp?.toString() || '0',
        mileage: device.mileage?.toString() || '0',
        direction: device.direction || '0',
        updateTime: device.updateTime || device.gpsTime || new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting E-Seal position data:', error);
      return null;
    }
  }

  /**
   * Test GPS API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🧪 Testing GPS API connection...');
      const devices = await this.getAllDevices();
      console.log(`✅ GPS API connection test successful - found ${devices.length} devices`);
      return true;
    } catch (error) {
      console.error('❌ GPS API connection test failed:', error);
      return false;
    }
  }
}
