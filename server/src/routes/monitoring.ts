import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';
import { auth } from '../auth';
import { GpsService } from '../services/GpsService';
import { BeacukaiApiService } from '../services/BeacukaiApiService';
import { GeocodingService } from '../services/GeocodingService';

const prisma = new PrismaClient();
const gpsService = new GpsService(prisma);
const beacukaiService = new BeacukaiApiService(prisma);
const geocodingService = new GeocodingService(prisma);

// Event mapping based on seal.md LAMPIRAN 2
const EVENT_MAPPING: { [key: string]: string } = {
  '0': 'Normal, tidak ada event',
  '1': 'Locked',
  '2': 'Unlocked',
  '3': 'Cut the rope'
};

// Status mapping based on seal.md LAMPIRAN 5
const STATUS_MAPPING: { [key: string]: string } = {
  '0': 'INACTIVE',
  '1': 'ACTIVE'
};

// Helper function to get event label
const getEventLabel = (eventCode: string): string => {
  return EVENT_MAPPING[eventCode] || eventCode || 'Unknown';
};

// Helper function to get status label
const getStatusLabel = (statusCode: string): string => {
  return STATUS_MAPPING[statusCode] || statusCode || 'Unknown';
};

export const monitoringRoutes = new Hono()

// Get all active E-Seals with real-time monitoring data
.get('/active-eseals', async (c) => {
  try {
    const session = await auth.api.getSession({ headers: c.req.header() });
    if (!session?.user) {
      return c.json({
        success: false,
        error: 'User authentication required'
      }, 401);
    }

    const organizationSlug = c.req.query('organizationSlug');
    const userRole = (session.user as any).role;

    console.log('📊 Fetching active E-Seals for monitoring, role:', userRole, 'org:', organizationSlug);

    // Build where clause based on user role and organization
    let whereClause: any = {
      // Only get E-Seals that have active tracking sessions or are in ACTIVE status
      OR: [
        { status: 'ACTIVE' },
        {
          trackingSessions: {
            some: {
              sessionStatus: 'ACTIVE'
            }
          }
        }
      ]
    };

    // Apply organization filter for admin users
    if (userRole?.includes('admin') && !userRole?.includes('superadmin')) {
      if (!organizationSlug) {
        return c.json({
          success: false,
          error: 'Organization slug required for admin users'
        }, 400);
      }

      // Get organization by slug
      const organization = await prisma.organization.findUnique({
        where: { slug: organizationSlug }
      });

      if (!organization) {
        return c.json({
          success: false,
          error: 'Organization not found'
        }, 404);
      }

      whereClause.organizationId = organization.id;
    }

    // Fetch active E-Seals with all related data
    const activeESeals = await prisma.eSeal.findMany({
      where: whereClause,
      include: {
        deviceStatus: true,
        trackingSessions: {
          where: { sessionStatus: 'ACTIVE' },
          orderBy: { startedAt: 'desc' },
          take: 1
        },
        trackingLogs: {
          orderBy: { createdAt: 'desc' },
          take: 1 // Get latest position
        },
        user: {
          select: { name: true, email: true }
        },
        organization: {
          select: { name: true, slug: true }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    console.log(`📊 Found ${activeESeals.length} active E-Seals`);

    // Transform data for monitoring display
    const monitoringData = await Promise.all(
      activeESeals.map(async (eseal, index) => {
        const deviceStatus = eseal.deviceStatus;
        const latestLog = eseal.trackingLogs[0];
        const activeSession = eseal.trackingSessions[0];

        // Get real-time GPS data if available
        let gpsData = null;
        try {
          if (eseal.gpsDeviceId) {
            gpsData = await gpsService.getESealPositionData(eseal.id);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to get GPS data for E-Seal ${eseal.noEseal}:`, error);
        }

        // Use GPS data if available, otherwise fall back to device status or tracking log
        const currentLat = gpsData?.latitude || deviceStatus?.lastKnownLat || latestLog?.latitude || '0';
        const currentLng = gpsData?.longitude || deviceStatus?.lastKnownLng || latestLog?.longitude || '0';

        // Convert coordinates to address if we have valid coordinates
        let addressInfo = {
          formattedAddress: 'Lokasi tidak diketahui',
          city: '',
          province: ''
        };

        if (currentLat !== '0' && currentLng !== '0') {
          try {
            const geocoded = await geocodingService.reverseGeocode(currentLat, currentLng);
            addressInfo = {
              formattedAddress: geocoded.formattedAddress,
              city: geocoded.city || '',
              province: geocoded.province || ''
            };
          } catch (error) {
            console.warn(`⚠️ Failed to geocode ${currentLat}, ${currentLng}:`, error);
            // Use existing address if geocoding fails
            addressInfo.formattedAddress = gpsData?.address || deviceStatus?.lastKnownAddress || latestLog?.address || `${currentLat}, ${currentLng}`;
            addressInfo.city = deviceStatus?.kota || latestLog?.kota || '';
            addressInfo.province = deviceStatus?.provinsi || latestLog?.provinsi || '';
          }
        }

        // Determine online status
        const isOnline = deviceStatus?.gpsOnline || false;
        const lastUpdate = deviceStatus?.lastGpsUpdate || latestLog?.createdAt;

        // Get event and status with proper mapping
        const eventCode = gpsData?.event || deviceStatus?.event || latestLog?.event || '0';
        const eventLabel = getEventLabel(eventCode);

        return {
          id: eseal.id,
          no: index + 1,
          icon: '📍',
          noESeal: eseal.noEseal,
          noIMEI: eseal.noImei,
          address: addressInfo.formattedAddress,
          posisiAltitude: gpsData?.altitude || deviceStatus?.lastKnownAltitude || latestLog?.altitude || '0',
          dayaBaterai: gpsData?.battery || deviceStatus?.batteryLevel || latestLog?.battery || '0',
          dayaAki: deviceStatus?.dayaAki || latestLog?.dayaAki || '0',
          event: eventLabel,
          eventCode: eventCode, // Keep original code for frontend logic
          idVendor: eseal.idVendor,
          kecepatanKontainer: gpsData?.speed || deviceStatus?.lastKnownSpeed || latestLog?.speed || '0',
          posisiLatitude: currentLat,
          posisiLongitude: currentLng,
          provinsi: addressInfo.province,
          kota: addressInfo.city,
          token: eseal.idVendor, // Use vendor token as per seal.md
          status: isOnline ? 'ONLINE' : 'OFFLINE',
          lockStatus: deviceStatus?.lockStatus || latestLog?.lockStatus || 'UNKNOWN',
          signalStrength: deviceStatus?.signalStrength || latestLog?.signalStrength || '0',
          lastUpdate: lastUpdate?.toISOString(),
          trackingStatus: activeSession ? 'TRACKING' : 'IDLE',
          organization: eseal.organization?.name || 'N/A'
        };
      })
    );

    return c.json({
      success: true,
      data: monitoringData,
      total: monitoringData.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching active E-Seals:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
})

// Get specific E-Seal monitoring data
.get('/eseal/:id/status', async (c) => {
  try {
    const esealId = c.req.param('id');
    const session = await auth.api.getSession({ headers: c.req.header() });

    if (!session?.user) {
      return c.json({
        success: false,
        error: 'User authentication required'
      }, 401);
    }

    console.log('📊 Fetching monitoring status for E-Seal:', esealId);

    const eseal = await prisma.eSeal.findUnique({
      where: { id: esealId },
      include: {
        deviceStatus: true,
        trackingSessions: {
          where: { sessionStatus: 'ACTIVE' },
          orderBy: { startedAt: 'desc' },
          take: 1
        },
        trackingLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10 // Get recent positions
        }
      }
    });

    if (!eseal) {
      return c.json({
        success: false,
        error: 'E-Seal not found'
      }, 404);
    }

    // Get real-time GPS data
    let gpsData = null;
    try {
      if (eseal.gpsDeviceId) {
        gpsData = await gpsService.getESealPositionData(eseal.id);
      }
    } catch (error) {
      console.warn(`⚠️ Failed to get GPS data for E-Seal ${eseal.noEseal}:`, error);
    }

    // Get Beacukai tracking status
    let beacukaiStatus = null;
    try {
      beacukaiStatus = await beacukaiService.getTrackingStatus(
        eseal.idVendor,
        eseal.noEseal,
        '919253c8-d0e1-4780-89d0-e91f77e89855' // Default token
      );
    } catch (error) {
      console.warn(`⚠️ Failed to get Beacukai status for E-Seal ${eseal.noEseal}:`, error);
    }

    const deviceStatus = eseal.deviceStatus;
    const activeSession = eseal.trackingSessions[0];

    return c.json({
      success: true,
      data: {
        eseal: {
          id: eseal.id,
          noEseal: eseal.noEseal,
          noImei: eseal.noImei,
          idVendor: eseal.idVendor,
          status: eseal.status
        },
        currentPosition: {
          latitude: gpsData?.latitude || deviceStatus?.lastKnownLat || '0',
          longitude: gpsData?.longitude || deviceStatus?.lastKnownLng || '0',
          address: gpsData?.address || deviceStatus?.lastKnownAddress || 'Lokasi tidak diketahui',
          altitude: gpsData?.altitude || deviceStatus?.lastKnownAltitude || '0',
          speed: gpsData?.speed || deviceStatus?.lastKnownSpeed || '0'
        },
        deviceInfo: {
          battery: gpsData?.battery || deviceStatus?.batteryLevel || '0',
          dayaAki: deviceStatus?.dayaAki || '0',
          signalStrength: deviceStatus?.signalStrength || '0',
          temperature: deviceStatus?.temperature || '0',
          lockStatus: deviceStatus?.lockStatus || 'UNKNOWN',
          event: gpsData?.event || deviceStatus?.event || 'UNKNOWN',
          online: deviceStatus?.gpsOnline || false,
          lastUpdate: deviceStatus?.lastGpsUpdate?.toISOString()
        },
        trackingInfo: {
          isTracking: !!activeSession,
          sessionId: activeSession?.id,
          startedAt: activeSession?.startedAt?.toISOString(),
          beacukaiStatus: beacukaiStatus?.status || 'unknown'
        },
        recentPositions: eseal.trackingLogs.map(log => ({
          latitude: log.latitude,
          longitude: log.longitude,
          address: log.address,
          timestamp: log.createdAt.toISOString(),
          speed: log.speed,
          event: log.event
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching E-Seal status:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
})

// Get real-time positions for all active E-Seals (for map display)
.get('/real-time-positions', async (c) => {
  try {
    const session = await auth.api.getSession({ headers: c.req.header() });
    if (!session?.user) {
      return c.json({
        success: false,
        error: 'User authentication required'
      }, 401);
    }

    const organizationSlug = c.req.query('organizationSlug');
    const userRole = (session.user as any).role;

    console.log('🗺️ Fetching real-time positions for map display');

    // Build where clause based on user role and organization
    let whereClause: any = {
      OR: [
        { status: 'ACTIVE' },
        {
          trackingSessions: {
            some: {
              sessionStatus: 'ACTIVE'
            }
          }
        }
      ]
    };

    // Apply organization filter for admin users
    if (userRole?.includes('admin') && !userRole?.includes('superadmin')) {
      if (!organizationSlug) {
        return c.json({
          success: false,
          error: 'Organization slug required for admin users'
        }, 400);
      }

      const organization = await prisma.organization.findUnique({
        where: { slug: organizationSlug }
      });

      if (!organization) {
        return c.json({
          success: false,
          error: 'Organization not found'
        }, 404);
      }

      whereClause.organizationId = organization.id;
    }

    const activeESeals = await prisma.eSeal.findMany({
      where: whereClause,
      include: {
        deviceStatus: true,
        trackingLogs: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    // Get positions for map markers
    const positions = await Promise.all(
      activeESeals.map(async (eseal) => {
        const deviceStatus = eseal.deviceStatus;
        const latestLog = eseal.trackingLogs[0];

        // Get real-time GPS data if available
        let gpsData = null;
        try {
          if (eseal.gpsDeviceId) {
            gpsData = await gpsService.getESealPositionData(eseal.id);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to get GPS data for E-Seal ${eseal.noEseal}:`, error);
        }

        const lat = parseFloat(gpsData?.latitude || deviceStatus?.lastKnownLat || latestLog?.latitude || '0');
        const lng = parseFloat(gpsData?.longitude || deviceStatus?.lastKnownLng || latestLog?.longitude || '0');

        // Only return positions with valid coordinates
        if (lat === 0 && lng === 0) {
          return null;
        }

        return {
          id: eseal.id,
          noEseal: eseal.noEseal,
          lat,
          lng,
          address: gpsData?.address || deviceStatus?.lastKnownAddress || latestLog?.address || 'Lokasi tidak diketahui',
          status: deviceStatus?.gpsOnline ? 'ONLINE' : 'OFFLINE',
          lockStatus: deviceStatus?.lockStatus || 'UNKNOWN',
          speed: gpsData?.speed || deviceStatus?.lastKnownSpeed || latestLog?.speed || '0',
          lastUpdate: deviceStatus?.lastGpsUpdate?.toISOString() || latestLog?.createdAt?.toISOString()
        };
      })
    );

    // Filter out null positions
    const validPositions = positions.filter(pos => pos !== null);

    return c.json({
      success: true,
      data: validPositions,
      total: validPositions.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching real-time positions:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});
