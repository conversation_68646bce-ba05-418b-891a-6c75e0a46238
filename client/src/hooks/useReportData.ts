import { useState, useEffect } from 'react';

export interface ReportData {
  id: string;
  no: number;
  noESeal: string;
  noAju: string;
  noIMEI: string;
  event: string;
  count: number;
  duration: string;
  mileage: number;
  maxSpeed: number;
  avgSpeed: number;
  startDateTime: string;
  endDateTime: string;
  carId?: number;
  carName?: string;
  driverName?: string;
  startLat?: number;
  startLng?: number;
  endLat?: number;
  endLng?: number;
}

interface UseReportDataParams {
  selectedEseal?: string;
  startDate?: string;
  endDate?: string;
  organizationSlug?: string;
}

export const useReportData = (params: UseReportDataParams) => {
  const [data, setData] = useState<ReportData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReportData = async () => {
      if (!params.selectedEseal || !params.startDate || !params.endDate) {
        setData([]);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // For now, we'll use the GPS API to get real tracking data
        // This should be replaced with actual report API calls to lock.md endpoints
        const response = await fetch('/api/gps/reports', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            deviceId: params.selectedEseal,
            startTime: params.startDate,
            endTime: params.endDate,
            organizationSlug: params.organizationSlug,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch report data');
        }

        const result = await response.json();
        
        // Transform the data to match our interface
        const transformedData: ReportData[] = result.data?.map((item: any, index: number) => ({
          id: item.id || `report-${index}`,
          no: index + 1,
          noESeal: item.deviceId || params.selectedEseal || '',
          noAju: item.ajuNumber || 'AJU' + Math.random().toString().substr(2, 12),
          noIMEI: item.imei || item.deviceId || '',
          event: item.event || (item.locked ? 'LOCKED' : 'UNLOCKED'),
          count: item.eventCount || 1,
          duration: item.duration || calculateDuration(item.startTime, item.endTime),
          mileage: item.mileage || item.distance || 0,
          maxSpeed: item.maxSpeed || item.topSpeed || 0,
          avgSpeed: item.avgSpeed || item.averageSpeed || 0,
          startDateTime: item.startTime || item.gpsTime || new Date().toISOString(),
          endDateTime: item.endTime || item.gpsTime || new Date().toISOString(),
          carId: item.carId,
          carName: item.carName || item.vehicleName,
          driverName: item.driverName,
          startLat: item.startLat || item.lat,
          startLng: item.startLng || item.lng,
          endLat: item.endLat || item.lat,
          endLng: item.endLng || item.lng,
        })) || [];

        setData(transformedData);
      } catch (err) {
        console.error('Error fetching report data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        
        // Fallback to dummy data for development
        setData(generateDummyReportData(params.selectedEseal));
      } finally {
        setLoading(false);
      }
    };

    fetchReportData();
  }, [params.selectedEseal, params.startDate, params.endDate, params.organizationSlug]);

  return { data, loading, error };
};

// Helper function to calculate duration
const calculateDuration = (startTime?: string, endTime?: string): string => {
  if (!startTime || !endTime) return '00 h 00 m 00 s';
  
  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = end.getTime() - start.getTime();
  
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
  
  return `${hours.toString().padStart(2, '0')} h ${minutes.toString().padStart(2, '0')} m ${seconds.toString().padStart(2, '0')} s`;
};

// Generate dummy data based on selected E-Seal
const generateDummyReportData = (selectedEseal?: string): ReportData[] => {
  if (!selectedEseal) return [];

  const baseData = [
    {
      id: '1',
      no: 1,
      noESeal: selectedEseal,
      noAju: 'AJU123457684663',
      noIMEI: '875298572967967922',
      event: 'UNLOCKED',
      count: 1,
      duration: '01 h 44 m 25 s',
      mileage: 28,
      maxSpeed: 41,
      avgSpeed: 17.74,
      startDateTime: '2025-01-12 08:00:00',
      endDateTime: '2025-01-12 09:44:25',
      startLat: -6.9175,
      startLng: 110.1625,
      endLat: -7.2575,
      endLng: 112.7521,
    },
    {
      id: '2',
      no: 2,
      noESeal: selectedEseal,
      noAju: 'AJU123457684664',
      noIMEI: '875298572967967922',
      event: 'LOCKED',
      count: 1,
      duration: '02 h 15 m 30 s',
      mileage: 35,
      maxSpeed: 55,
      avgSpeed: 22.5,
      startDateTime: '2025-01-12 10:00:00',
      endDateTime: '2025-01-12 12:15:30',
      startLat: -6.9667,
      startLng: 110.4167,
      endLat: -7.7956,
      endLng: 110.3695,
    },
    {
      id: '3',
      no: 3,
      noESeal: selectedEseal,
      noAju: 'AJU123457684665',
      noIMEI: '875298572967967922',
      event: 'UNLOCKED',
      count: 2,
      duration: '03 h 22 m 15 s',
      mileage: 42,
      maxSpeed: 65,
      avgSpeed: 28.3,
      startDateTime: '2025-01-12 13:00:00',
      endDateTime: '2025-01-12 16:22:15',
      startLat: -7.7956,
      startLng: 110.3695,
      endLat: -6.2088,
      endLng: 106.8456,
    }
  ];

  return baseData;
};
