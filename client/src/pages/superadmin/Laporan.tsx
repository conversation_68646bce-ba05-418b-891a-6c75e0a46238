import React, { useState } from 'react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Download, Save, Search, BarChart3, Map, Truck, Clock, FileText, Settings } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { useESealData } from '../../hooks/useESealData';
import { useReportData } from '../../hooks/useReportData';
import <PERSON>poran<PERSON><PERSON> from '../../components/charts/LaporanChart';
import SimpleRouteMap from '../../components/map/SimpleRouteMap';


// Track data for map replay
const generateTrackData = (reportData: any[]) => {
  if (reportData.length === 0) return [];

  const firstItem = reportData[0];
  return [
    { lat: firstItem.startLat || -6.2088, lng: firstItem.startLng || 106.8456, timestamp: firstItem.startDateTime, speed: 0 },
    { lat: firstItem.endLat || -6.2400, lng: firstItem.endLng || 106.8970, timestamp: firstItem.endDateTime, speed: firstItem.maxSpeed || 0 }
  ];
};



const Laporan: React.FC = () => {
  const [selectedEseal, setSelectedEseal] = useState('');
  const [searchEseal, setSearchEseal] = useState('');
  const [periodType, setPeriodType] = useState('today'); // today, yesterday, thisMonth, lastMonth, custom
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [exportAddress, setExportAddress] = useState(false);
  const [viewMode, setViewMode] = useState<'chart' | 'map'>('chart');

  // Get E-Seal data for all organizations (superadmin)
  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000,
    search: searchEseal,
  });

  // Get report data based on selected E-Seal and date range
  const { data: reportData, loading: reportLoading } = useReportData({
    selectedEseal,
    startDate,
    endDate,
  });

  // Calculate chart data from report results
  const chartData = reportData.map(item => ({
    noESeal: item.noESeal,
    mileage: item.mileage.toString()
  }));

  // Generate track data from report data
  const trackData = generateTrackData(reportData);

  const handleDownload = () => {
    if (!selectedEseal || (!startDate && !endDate)) {
      alert('Silakan pilih E-Seal dan periode tanggal terlebih dahulu.');
      return;
    }
    // Dummy download logic
    alert(`Downloading report for ${selectedEseal} from ${startDate} to ${endDate}, export with address: ${exportAddress}`);
  };

  const handleSave = () => {
    // Implement save functionality
    console.log('Saving report...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Laporan</h1>
        <div className="h-1 w-20 bg-slate-800 rounded"></div>
      </div>

      {/* Filter Section */}
      <div className="bg-white p-6 rounded-lg border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          {/* E-Seal Selection with Searchable Dropdown */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Truck className="w-4 h-4" />
              Pilih E-Seal
            </label>
            <Select value={selectedEseal} onValueChange={setSelectedEseal}>
              <SelectTrigger>
                <SelectValue placeholder="Cari E-Seal..." />
              </SelectTrigger>
              <SelectContent>
                <div className="p-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Cari E-Seal..."
                      value={searchEseal}
                      onChange={(e) => setSearchEseal(e.target.value)}
                      className="pl-10 mb-2"
                    />
                  </div>
                </div>
                {esealData?.filter(eseal =>
                  eseal.noEseal?.toLowerCase().includes(searchEseal.toLowerCase())
                ).map(eseal => (
                  <SelectItem key={eseal.id} value={eseal.noEseal!}>
                    <div className="flex items-center gap-2">
                      <span className="text-blue-600">✓</span>
                      {eseal.noEseal}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Period Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Periode
            </label>
            <Select value={periodType} onValueChange={setPeriodType}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih Periode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Hari Ini</SelectItem>
                <SelectItem value="yesterday">Kemarin</SelectItem>
                <SelectItem value="thisMonth">Bulan Ini</SelectItem>
                <SelectItem value="lastMonth">Bulan Kemarin</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Actions */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Aksi
            </label>
            <div className="flex gap-2">
              <Button onClick={handleDownload} className="bg-slate-800 hover:bg-slate-700 text-white">
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>

        {/* Custom Date Range - Show only when Custom Range is selected */}
        {periodType === 'custom' && (
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Tanggal Mulai
                </label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Tanggal Selesai
                </label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
          </div>
        )}

        {/* Export Options */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="exportAddress"
              checked={exportAddress}
              onChange={(e) => setExportAddress(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="exportAddress" className="text-sm text-gray-700">
              Export dengan alamat lengkap
            </label>
          </div>
        </div>
      </div>

      {/* Chart Section - Always show when E-Seal is selected */}
      {selectedEseal && (
        <div className="bg-white p-6 rounded-lg border mb-6">
          <h3 className="text-lg font-semibold mb-4">Grafik Mileage E-Seal</h3>
          <div className="h-80">
            <LaporanChart data={chartData} />
          </div>
        </div>
      )}

      {/* Table Section */}
      <div className="bg-white rounded-lg border overflow-hidden mb-6">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold">Data Laporan E-Seal</h3>
          <p className="text-sm text-gray-600 mt-1">
            Menampilkan {reportData.length} data laporan
          </p>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No Aju</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Count</TableHead>
                <TableHead className="font-semibold text-white">Duration</TableHead>
                <TableHead className="font-semibold text-white">Mileage</TableHead>
                <TableHead className="font-semibold text-white">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                    {selectedEseal || startDate || endDate
                      ? 'Tidak ada data yang sesuai dengan filter'
                      : 'Pilih E-Seal dan periode untuk melihat laporan'
                    }
                  </TableCell>
                </TableRow>
              ) : (
                reportData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noAju}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.event === 'UNLOCKED' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">{item.count}</TableCell>
                    <TableCell className="font-mono text-sm">{item.duration}</TableCell>
                    <TableCell className="font-mono text-sm">{item.mileage} km</TableCell>
                    <TableCell className="font-mono text-sm">{item.maxSpeed}</TableCell>
                    <TableCell className="font-mono text-sm">{item.avgSpeed}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Map Section - Always show when E-Seal is selected */}
      {selectedEseal && trackData.length > 0 && (
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Rute Perjalanan E-Seal</h3>
          <div className="h-96">
            <SimpleRouteMap
              trackData={trackData}
              height="100%"
            />
          </div>
        </div>
      )}


    </div>
  );
};

export default Laporan;
