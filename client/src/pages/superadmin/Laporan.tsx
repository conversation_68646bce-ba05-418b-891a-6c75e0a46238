import React, { useState, useEffect } from 'react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Download, Save, Search, BarChart3, Map, Truck, Clock } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { useESealData } from '../../hooks/useESealData';
import LaporanChart from '../../components/charts/LaporanChart';
import MapTilerReplay from '../../components/map/MapTilerReplay';


// Dummy data untuk laporan sesuai spesifikasi
const dummyLaporanData = [
  {
    id: '1',
    no: 1,
    noESeal: '7848585252',
    noAju: 'AJU123457684663',
    noIMEI: '875298572967967922',
    event: 'UNLOCKED',
    count: 1,
    duration: '01 h 44 m 25 s',
    mileage: '28',
    maxSpeed: '41',
    avgSpeed: '17.73668514',
    startDateTime: '2025-07-07 17:02:18',
    endDateTime: '2025-07-07 17:07:18',
    startMileage: '198',
    endMileage: '200',
    tripMileage: '2',
    tripDuration: '01 h 44 m 25 s',
    driverName: 'Budi Santosa',
    startAddress: 'Kendal, Jawa Tengah',
    endAddress: 'Surabaya, Jawa Timur',
    startLat: '-6.9175',
    startLng: '110.1625',
    endLat: '-7.2575',
    endLng: '112.7521'
  },
  {
    id: '2',
    no: 2,
    noESeal: '7612340022',
    noAju: 'AJU123457684663',
    noIMEI: '875298572967967922',
    event: 'LOCKED',
    count: 1,
    duration: '02 h 15 m 30 s',
    mileage: '35',
    maxSpeed: '55',
    avgSpeed: '22.5',
    startDateTime: '2025-07-08 09:15:30',
    endDateTime: '2025-07-08 11:31:00',
    startMileage: '250',
    endMileage: '285',
    tripMileage: '35',
    tripDuration: '02 h 15 m 30 s',
    driverName: 'Ahmad Wijaya',
    startAddress: 'Semarang, Jawa Tengah',
    endAddress: 'Yogyakarta, DIY',
    startLat: '-6.9667',
    startLng: '110.4167',
    endLat: '-7.7956',
    endLng: '110.3695'
  }
];



const Laporan: React.FC = () => {
  const [selectedEseal, setSelectedEseal] = useState('');
  const [searchEseal, setSearchEseal] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [exportAddress, setExportAddress] = useState(false);
  const [viewMode, setViewMode] = useState<'chart' | 'map'>('chart');
  const [filteredData, setFilteredData] = useState(dummyLaporanData);

  // Get E-Seal data for all organizations (superadmin)
  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000,
    search: searchEseal,
  });

  // Filter data based on selected E-Seal and date range
  useEffect(() => {
    let filtered = dummyLaporanData;

    // Filter by selected E-Seal
    if (selectedEseal) {
      filtered = filtered.filter(item => item.noESeal === selectedEseal);
    }

    // Filter by date range
    if (startDate && endDate) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.startDateTime);
        const start = new Date(startDate);
        const end = new Date(endDate);
        return itemDate >= start && itemDate <= end;
      });
    }

    setFilteredData(filtered);
  }, [selectedEseal, startDate, endDate]);

  // Calculate chart data from filtered results
  const chartData = filteredData.map(item => ({
    noESeal: item.noESeal,
    mileage: item.mileage
  }));

  // Dummy track data for replay - Jakarta to Bekasi route
  const dummyTrackData = [
    { lat: -6.2088, lng: 106.8456, timestamp: '2025-07-07 08:00:00', speed: 0 }, // Jakarta start
    { lat: -6.2094, lng: 106.8462, timestamp: '2025-07-07 08:05:00', speed: 15 },
    { lat: -6.2102, lng: 106.8475, timestamp: '2025-07-07 08:10:00', speed: 25 },
    { lat: -6.2115, lng: 106.8495, timestamp: '2025-07-07 08:15:00', speed: 35 },
    { lat: -6.2130, lng: 106.8520, timestamp: '2025-07-07 08:20:00', speed: 40 },
    { lat: -6.2145, lng: 106.8545, timestamp: '2025-07-07 08:25:00', speed: 45 },
    { lat: -6.2160, lng: 106.8570, timestamp: '2025-07-07 08:30:00', speed: 50 },
    { lat: -6.2175, lng: 106.8595, timestamp: '2025-07-07 08:35:00', speed: 55 },
    { lat: -6.2190, lng: 106.8620, timestamp: '2025-07-07 08:40:00', speed: 60 },
    { lat: -6.2205, lng: 106.8645, timestamp: '2025-07-07 08:45:00', speed: 58 },
    { lat: -6.2220, lng: 106.8670, timestamp: '2025-07-07 08:50:00', speed: 52 },
    { lat: -6.2235, lng: 106.8695, timestamp: '2025-07-07 08:55:00', speed: 48 },
    { lat: -6.2250, lng: 106.8720, timestamp: '2025-07-07 09:00:00', speed: 45 },
    { lat: -6.2265, lng: 106.8745, timestamp: '2025-07-07 09:05:00', speed: 42 },
    { lat: -6.2280, lng: 106.8770, timestamp: '2025-07-07 09:10:00', speed: 38 },
    { lat: -6.2295, lng: 106.8795, timestamp: '2025-07-07 09:15:00', speed: 35 },
    { lat: -6.2310, lng: 106.8820, timestamp: '2025-07-07 09:20:00', speed: 30 },
    { lat: -6.2325, lng: 106.8845, timestamp: '2025-07-07 09:25:00', speed: 25 },
    { lat: -6.2340, lng: 106.8870, timestamp: '2025-07-07 09:30:00', speed: 20 },
    { lat: -6.2355, lng: 106.8895, timestamp: '2025-07-07 09:35:00', speed: 15 },
    { lat: -6.2370, lng: 106.8920, timestamp: '2025-07-07 09:40:00', speed: 10 },
    { lat: -6.2385, lng: 106.8945, timestamp: '2025-07-07 09:45:00', speed: 5 },
    { lat: -6.2400, lng: 106.8970, timestamp: '2025-07-07 09:50:00', speed: 0 }, // Bekasi end
  ];

  const handleDownload = () => {
    if (!selectedEseal || (!startDate && !endDate)) {
      alert('Silakan pilih E-Seal dan periode tanggal terlebih dahulu.');
      return;
    }
    // Dummy download logic
    alert(`Downloading report for ${selectedEseal} from ${startDate} to ${endDate}, export with address: ${exportAddress}`);
  };

  const handleSave = () => {
    // Implement save functionality
    console.log('Saving report...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Laporan</h1>
        <div className="h-1 w-20 bg-slate-800 rounded"></div>
      </div>

      {/* Filter Section */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4 items-end">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">PILIH NOMOR E-SEAL</span>
            </div>
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari E-Seal..."
                  value={searchEseal}
                  onChange={(e) => setSearchEseal(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedEseal} onValueChange={setSelectedEseal}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih E-Seal" />
                </SelectTrigger>
                <SelectContent>
                  {esealData?.filter(eseal =>
                    eseal.noEseal?.toLowerCase().includes(searchEseal.toLowerCase())
                  ).map(eseal => (
                    <SelectItem key={eseal.id} value={eseal.noEseal!}>
                      {eseal.noEseal}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">PERIODE</span>
            </div>
            <Input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleDownload} className="bg-slate-800 hover:bg-slate-700 text-white">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button onClick={handleSave} className="bg-slate-800 hover:bg-slate-700 text-white">
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="exportAddress"
              checked={exportAddress}
              onChange={(e) => setExportAddress(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="exportAddress" className="text-sm text-gray-700">
              Export with Address
            </label>
          </div>
        </div>
      </div>

      {/* Action Icons */}
      <div className="flex gap-2">
        <Button variant="outline" size="sm" className="p-2">
          <FileText className="w-4 h-4" />
        </Button>
        <Button
          variant={viewMode === 'chart' ? 'default' : 'outline'}
          size="sm"
          className="p-2"
          onClick={() => setViewMode('chart')}
        >
          <BarChart3 className="w-4 h-4" />
        </Button>
        <Button
          variant={viewMode === 'map' ? 'default' : 'outline'}
          size="sm"
          className="p-2"
          onClick={() => setViewMode('map')}
        >
          <Map className="w-4 h-4" />
        </Button>
        <div className="ml-auto flex gap-2">
          <Button variant="outline" size="sm" className="p-2">
            <Settings className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="p-2">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Chart Section */}
      {viewMode === 'chart' && (
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Grafik Mileage E-Seal</h3>
          <div className="h-80">
            <LaporanChart data={chartData} />
          </div>
        </div>
      )}

      {/* Table Section */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold">Data Laporan E-Seal</h3>
          <p className="text-sm text-gray-600 mt-1">
            Menampilkan {filteredData.length} dari {dummyLaporanData.length} data
          </p>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No Aju</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Count</TableHead>
                <TableHead className="font-semibold text-white">Duration</TableHead>
                <TableHead className="font-semibold text-white">Mileage</TableHead>
                <TableHead className="font-semibold text-white">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                    {selectedEseal || startDate || endDate
                      ? 'Tidak ada data yang sesuai dengan filter'
                      : 'Pilih E-Seal dan periode untuk melihat laporan'
                    }
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noAju}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.event === 'UNLOCKED' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">{item.count}</TableCell>
                    <TableCell className="font-mono text-sm">{item.duration}</TableCell>
                    <TableCell className="font-mono text-sm">{item.mileage} km</TableCell>
                    <TableCell className="font-mono text-sm">{item.maxSpeed}</TableCell>
                    <TableCell className="font-mono text-sm">{item.avgSpeed}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Map Section - Below table as requested */}
      {viewMode === 'map' && (
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Replay Track E-Seal</h3>
          <div className="h-96">
            <MapTilerReplay
              trackData={dummyTrackData}
              height="100%"
            />
          </div>
        </div>
      )}


    </div>
  );
};

export default Laporan;
