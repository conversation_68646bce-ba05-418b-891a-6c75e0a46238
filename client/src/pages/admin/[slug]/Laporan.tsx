import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Download, Save, Search, BarChart3, Map, Truck, Clock, FileText, Settings } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { useESealData } from '../../../hooks/useESealData';
import { useReportData } from '../../../hooks/useReportData';
import Laporan<PERSON><PERSON> from '../../../components/charts/LaporanChart';
import MapTilerReplay from '../../../components/map/MapTilerReplay';


// Track data generator for map replay
const generateTrackData = (reportData: any[]) => {
  if (reportData.length === 0) return [];

  const firstItem = reportData[0];
  return [
    { lat: firstItem.startLat || -6.2088, lng: firstItem.startLng || 106.8456, timestamp: firstItem.startDateTime, speed: 0 },
    { lat: firstItem.endLat || -6.2400, lng: firstItem.endLng || 106.8970, timestamp: firstItem.endDateTime, speed: firstItem.maxSpeed || 0 }
  ];
};



const Laporan: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [selectedEseal, setSelectedEseal] = useState('');
  const [searchEseal, setSearchEseal] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [exportAddress, setExportAddress] = useState(false);
  const [viewMode, setViewMode] = useState<'chart' | 'map'>('chart');

  // Get E-Seal data for the organization
  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000,
    organizationSlug: slug,
    search: searchEseal,
  });

  // Get report data based on selected E-Seal and date range
  const { data: reportData } = useReportData({
    selectedEseal,
    startDate,
    endDate,
    organizationSlug: slug,
  });

  // Calculate chart data from report results
  const chartData = reportData.map(item => ({
    noESeal: item.noESeal,
    mileage: item.mileage.toString()
  }));

  // Generate track data from report data
  const trackData = generateTrackData(reportData);

  const handleDownload = () => {
    if (!selectedEseal || (!startDate && !endDate)) {
      alert('Silakan pilih E-Seal dan periode tanggal terlebih dahulu.');
      return;
    }
    // Dummy download logic
    alert(`Downloading report for ${selectedEseal} from ${startDate} to ${endDate}, export with address: ${exportAddress}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Laporan</h1>
        <div className="h-1 w-20 bg-slate-800 rounded"></div>
      </div>

      {/* Filter Section */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4 items-end">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">PILIH NOMOR E-SEAL</span>
            </div>
            <Select onValueChange={setSelectedEseal}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih E-Seal" />
              </SelectTrigger>
              <SelectContent>
                {esealData?.map(eseal => (
                  <SelectItem key={eseal.id} value={eseal.noEseal!}>
                    {eseal.noEseal}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">PERIODE</span>
            </div>
            <Input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleDownload} className="bg-slate-800 hover:bg-slate-700 text-white">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button className="bg-slate-800 hover:bg-slate-700 text-white">
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="exportAddress"
              checked={exportAddress}
              onChange={(e) => setExportAddress(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="exportAddress" className="text-sm text-gray-700">
              Export with Address
            </label>
          </div>
        </div>
      </div>

      {/* Action Icons */}
      <div className="flex gap-2">
        <Button variant="outline" size="sm" className="p-2">
          <FileText className="w-4 h-4" />
        </Button>
        <Button
          variant={viewMode === 'chart' ? 'default' : 'outline'}
          size="sm"
          className="p-2"
          onClick={() => setViewMode('chart')}
        >
          <BarChart3 className="w-4 h-4" />
        </Button>
        <Button
          variant={viewMode === 'map' ? 'default' : 'outline'}
          size="sm"
          className="p-2"
          onClick={() => setViewMode('map')}
        >
          <Map className="w-4 h-4" />
        </Button>
        <div className="ml-auto flex gap-2">
          <Button variant="outline" size="sm" className="p-2">
            <Settings className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="p-2">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Chart Section */}
      {viewMode === 'chart' && (
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Grafik Mileage E-Seal</h3>
          <div className="h-80">
            <LaporanChart data={chartData} />
          </div>
        </div>
      )}

      {/* Table Section */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold">Data Laporan E-Seal</h3>
          <p className="text-sm text-gray-600 mt-1">
            Menampilkan {reportData.length} data laporan
          </p>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No Aju</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Count</TableHead>
                <TableHead className="font-semibold text-white">Duration</TableHead>
                <TableHead className="font-semibold text-white">Mileage</TableHead>
                <TableHead className="font-semibold text-white">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                    {selectedEseal || startDate || endDate
                      ? 'Tidak ada data yang sesuai dengan filter'
                      : 'Pilih E-Seal dan periode untuk melihat laporan'
                    }
                  </TableCell>
                </TableRow>
              ) : (
                reportData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noAju}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.event === 'UNLOCKED' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">{item.count}</TableCell>
                    <TableCell className="font-mono text-sm">{item.duration}</TableCell>
                    <TableCell className="text-center">{item.mileage}</TableCell>
                    <TableCell className="text-center">{item.maxSpeed}</TableCell>
                    <TableCell className="text-center">{item.avgSpeed}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Map Section - Below table as requested */}
      {viewMode === 'map' && (
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Replay Track E-Seal</h3>
          <div className="h-96">
            <MapTilerReplay
              trackData={trackData}
              height="100%"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Laporan;
