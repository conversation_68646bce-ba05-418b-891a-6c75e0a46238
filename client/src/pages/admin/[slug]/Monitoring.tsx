import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search, RefreshCw, Wifi, WifiOff, Clock } from 'lucide-react';
import MapTiler from '../../../components/map/MapTiler';
import { useESealData } from '../../../hooks/useESealData';
import { useMonitoringData } from '../../../hooks/useMonitoringData';

const Monitoring: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [search, setSearch] = useState('');
  const [selectedEseals, setSelectedEseals] = useState<string[]>([]);

  // Use real-time monitoring data for the organization
  const {
    data: monitoringData,
    positions,
    loading,
    error,
    lastUpdate,
    refreshData,
    getOnlineCount,
    getTrackingCount,
    getTotalCount
  } = useMonitoringData({
    organizationSlug: slug,
    refreshInterval: 30000, // Refresh every 30 seconds
    autoRefresh: true
  });

  // Also get E-Seal data for the selection list
  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000,
    organizationSlug: slug,
    search,
  });

  const handleSelectEseal = (id: string) => {
    setSelectedEseals(prev =>
      prev.includes(id) ? prev.filter(esealId => esealId !== id) : [...prev, id]
    );
  };

  // Filter monitoring data based on selected E-Seals
  const filteredMonitoringData = selectedEseals.length > 0
    ? monitoringData.filter(item => selectedEseals.includes(item.id))
    : monitoringData;

  // Get selected E-Seals for map display
  const selectedESealData = selectedEseals.length > 0
    ? positions.filter(pos => selectedEseals.includes(pos.id))
    : positions;

  // Get center position for map
  const getCenterPosition = () => {
    if (selectedESealData.length === 0) {
      return { lat: -6.2088, lng: 106.8456, address: 'Jakarta, Indonesia' };
    }

    if (selectedESealData.length === 1) {
      const pos = selectedESealData[0];
      return { lat: pos.lat, lng: pos.lng, address: pos.address };
    }

    // Calculate center of multiple positions
    const avgLat = selectedESealData.reduce((sum, pos) => sum + pos.lat, 0) / selectedESealData.length;
    const avgLng = selectedESealData.reduce((sum, pos) => sum + pos.lng, 0) / selectedESealData.length;

    return {
      lat: avgLat,
      lng: avgLng,
      address: `${selectedESealData.length} E-Seals selected`
    };
  };

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6 h-full flex flex-col">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">Monitoring</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>Memuat data monitoring...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">Monitoring</h1>
          <p className="text-gray-600">Real-time monitoring E-Seal untuk organisasi {slug}</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* Status indicators */}
          <div className="flex items-center space-x-2 text-sm">
            <div className="flex items-center space-x-1">
              <Wifi className="w-4 h-4 text-green-600" />
              <span className="text-green-600">{getOnlineCount()} Online</span>
            </div>
            <div className="flex items-center space-x-1">
              <WifiOff className="w-4 h-4 text-red-600" />
              <span className="text-red-600">{getTotalCount() - getOnlineCount()} Offline</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4 text-blue-600" />
              <span className="text-blue-600">{getTrackingCount()} Tracking</span>
            </div>
          </div>
          <button
            onClick={refreshData}
            className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <WifiOff className="w-5 h-5 text-red-600" />
            <span className="text-red-800">Error: {error}</span>
          </div>
        </div>
      )}

      {/* Last update info */}
      {lastUpdate && (
        <div className="text-xs text-gray-500">
          Terakhir diperbarui: {new Date(lastUpdate).toLocaleString('id-ID')}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">
        {/* Left Panel */}
        <div className="lg:col-span-1 bg-white p-4 rounded-lg border flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Cari E-Seal..." className="pl-10" value={search} onChange={e => setSearch(e.target.value)} />
          </div>
          <div className="flex-grow space-y-2 overflow-y-auto">
            {esealData?.map(eseal => (
              <div key={eseal.id} className="flex items-center space-x-2">
                <input type="checkbox" id={eseal.id} checked={selectedEseals.includes(eseal.id)} onChange={() => handleSelectEseal(eseal.id)} />
                <label htmlFor={eseal.id} className="text-sm">{eseal.noEseal}</label>
                <span className={`text-xs ${eseal.status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'}`}>
                  {eseal.status === 'ACTIVE' ? 'ONLINE' : 'OFFLINE'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Map */}
        <div className="lg:col-span-2 bg-white p-4 rounded-lg border">
          <div className="mb-2 flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">Peta Real-time</h3>
            <div className="text-xs text-gray-500">
              {selectedESealData.length} E-Seal ditampilkan
            </div>
          </div>
          <MapTiler originLocation={getCenterPosition()} interactive={true} />
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">Icon</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Address</TableHead>
                <TableHead className="font-semibold text-white">Posisi Altitude</TableHead>
                <TableHead className="font-semibold text-white">Daya Baterai</TableHead>
                <TableHead className="font-semibold text-white">Daya Aki</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">ID Vendor</TableHead>
                <TableHead className="font-semibold text-white">Kecepatan Kontainer</TableHead>
                <TableHead className="font-semibold text-white">Posisi Latitude</TableHead>
                <TableHead className="font-semibold text-white">Posisi Longitude</TableHead>
                <TableHead className="font-semibold text-white">Provinsi</TableHead>
                <TableHead className="font-semibold text-white">Kota</TableHead>
                <TableHead className="font-semibold text-white">Token</TableHead>
                <TableHead className="font-semibold text-white">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMonitoringData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={17} className="text-center py-8 text-gray-500">
                    {selectedEseals.length === 0
                      ? 'Pilih E-Seal untuk dimonitor dari daftar di sebelah kiri'
                      : 'Tidak ada data monitoring untuk E-Seal yang dipilih'
                    }
                  </TableCell>
                </TableRow>
              ) : (
                filteredMonitoringData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.no}</TableCell>
                    <TableCell className="text-center text-lg">{item.icon}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                    <TableCell className="text-sm max-w-xs truncate" title={item.address}>
                      {item.address}
                    </TableCell>
                    <TableCell className="font-mono text-sm">{item.posisiAltitude}</TableCell>
                    <TableCell className="font-mono text-sm">{item.dayaBaterai}</TableCell>
                    <TableCell className="font-mono text-sm">{item.dayaAki}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.event === 'UNLOCKED' || item.event === '1' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event === '0' ? 'NORMAL' : item.event === '1' ? 'OFFLINE' : item.event}
                      </span>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{item.idVendor}</TableCell>
                    <TableCell className="font-mono text-sm">{item.kecepatanKontainer}</TableCell>
                    <TableCell className="font-mono text-sm">{item.posisiLatitude}</TableCell>
                    <TableCell className="font-mono text-sm">{item.posisiLongitude}</TableCell>
                    <TableCell className="text-sm">{item.provinsi || '-'}</TableCell>
                    <TableCell className="text-sm">{item.kota || '-'}</TableCell>
                    <TableCell className="font-mono text-sm">***</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        {item.status === 'ONLINE' ? (
                          <Wifi className="w-3 h-3 text-green-600" />
                        ) : (
                          <WifiOff className="w-3 h-3 text-red-600" />
                        )}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {item.status}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Monitoring;
